import json
import logging
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List
import uuid

# Configure logging
logger = logging.getLogger(__name__)

class AgentLogger:
    """
    Comprehensive logging system for AI validation agents with detailed
    agent-wise logging and complete error reporting capabilities.
    """

    def __init__(self, base_log_dir: str = "logs"):
        self.base_log_dir = Path(base_log_dir)
        self.base_log_dir.mkdir(parents=True, exist_ok=True)

        # Thread-safe logging
        self._lock = threading.Lock()

        # Initialize log files
        self.guardrail_log = self.base_log_dir / "guardrail_violations.json"
        self.agent_activity_log = self.base_log_dir / "agent_activity.json"
        self.error_log = self.base_log_dir / "errors.json"
        self.audit_log = self.base_log_dir / "audit_trail.json"

        # Initialize log files if they don't exist
        for log_file in [self.guardrail_log, self.agent_activity_log, self.error_log, self.audit_log]:
            if not log_file.exists():
                log_file.write_text("[]")

    def log_violation(self, entry: Dict[str, Any], agent_name: Optional[str] = None) -> str:
        """
        Log a guardrail violation with comprehensive details.

        Args:
            entry: Violation details
            agent_name: Name of the agent that triggered the violation

        Returns:
            Unique violation ID for tracking
        """
        violation_id = str(uuid.uuid4())

        log_entry = {
            "violation_id": violation_id,
            "timestamp": datetime.utcnow().isoformat(),
            "agent_name": agent_name or "unknown",
            "severity": entry.get("severity", "medium"),
            "policy": entry.get("policy", "unknown"),
            "description": entry.get("description", ""),
            "recommendation": entry.get("recommendation", ""),
            "context": entry.get("context", {}),
            "status": entry.get("status", "active")
        }

        self._append_to_log(self.guardrail_log, log_entry)
        logger.warning(f"Guardrail violation logged: {violation_id} - {entry.get('description', '')}")

        return violation_id

    def log_agent_activity(self, agent_name: str, activity_type: str,
                          details: Dict[str, Any], status: str = "success") -> str:
        """
        Log agent activity with detailed information.

        Args:
            agent_name: Name of the agent
            activity_type: Type of activity (parse, validate, etc.)
            details: Activity details
            status: Activity status (success, error, warning)

        Returns:
            Unique activity ID for tracking
        """
        activity_id = str(uuid.uuid4())

        log_entry = {
            "activity_id": activity_id,
            "timestamp": datetime.utcnow().isoformat(),
            "agent_name": agent_name,
            "activity_type": activity_type,
            "status": status,
            "details": details,
            "processing_time_ms": details.get("processing_time_ms", 0),
            "input_size": details.get("input_size", 0),
            "output_size": details.get("output_size", 0)
        }

        self._append_to_log(self.agent_activity_log, log_entry)
        logger.info(f"Agent activity logged: {agent_name} - {activity_type} - {status}")

        return activity_id

    def log_error(self, agent_name: str, error_type: str, error_message: str,
                  context: Optional[Dict[str, Any]] = None, stack_trace: Optional[str] = None) -> str:
        """
        Log detailed error information with complete error reports.

        Args:
            agent_name: Name of the agent where error occurred
            error_type: Type/category of error
            error_message: Error message
            context: Additional context information
            stack_trace: Full stack trace if available

        Returns:
            Unique error ID for tracking
        """
        error_id = str(uuid.uuid4())

        log_entry = {
            "error_id": error_id,
            "timestamp": datetime.utcnow().isoformat(),
            "agent_name": agent_name,
            "error_type": error_type,
            "error_message": error_message,
            "context": context or {},
            "stack_trace": stack_trace,
            "severity": self._determine_error_severity(error_type),
            "resolved": False
        }

        self._append_to_log(self.error_log, log_entry)
        logger.error(f"Error logged: {error_id} - {agent_name} - {error_message}")

        return error_id

    def log_audit_event(self, event_type: str, user_id: Optional[str],
                       details: Dict[str, Any]) -> str:
        """
        Log audit events for compliance and tracking.

        Args:
            event_type: Type of audit event
            user_id: User identifier if applicable
            details: Event details

        Returns:
            Unique audit event ID
        """
        audit_id = str(uuid.uuid4())

        log_entry = {
            "audit_id": audit_id,
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "user_id": user_id,
            "details": details,
            "ip_address": details.get("ip_address"),
            "session_id": details.get("session_id")
        }

        self._append_to_log(self.audit_log, log_entry)
        logger.info(f"Audit event logged: {audit_id} - {event_type}")

        return audit_id

    def get_agent_logs(self, agent_name: str, hours: int = 24) -> List[Dict[str, Any]]:
        """
        Retrieve logs for a specific agent within a time window.

        Args:
            agent_name: Name of the agent
            hours: Number of hours to look back

        Returns:
            List of log entries for the agent
        """
        cutoff_time = datetime.utcnow().timestamp() - (hours * 3600)
        agent_logs = []

        # Search activity logs
        activities = self._read_log(self.agent_activity_log)
        for activity in activities:
            if (activity.get("agent_name") == agent_name and
                datetime.fromisoformat(activity["timestamp"]).timestamp() > cutoff_time):
                agent_logs.append({"type": "activity", **activity})

        # Search error logs
        errors = self._read_log(self.error_log)
        for error in errors:
            if (error.get("agent_name") == agent_name and
                datetime.fromisoformat(error["timestamp"]).timestamp() > cutoff_time):
                agent_logs.append({"type": "error", **error})

        # Sort by timestamp
        agent_logs.sort(key=lambda x: x["timestamp"])
        return agent_logs

    def get_violation_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get summary of violations within a time window.

        Args:
            hours: Number of hours to look back

        Returns:
            Summary statistics of violations
        """
        cutoff_time = datetime.utcnow().timestamp() - (hours * 3600)
        violations = self._read_log(self.guardrail_log)

        recent_violations = [
            v for v in violations
            if datetime.fromisoformat(v["timestamp"]).timestamp() > cutoff_time
        ]

        summary = {
            "total_violations": len(recent_violations),
            "by_severity": {},
            "by_policy": {},
            "by_agent": {},
            "active_violations": 0
        }

        for violation in recent_violations:
            # Count by severity
            severity = violation.get("severity", "unknown")
            summary["by_severity"][severity] = summary["by_severity"].get(severity, 0) + 1

            # Count by policy
            policy = violation.get("policy", "unknown")
            summary["by_policy"][policy] = summary["by_policy"].get(policy, 0) + 1

            # Count by agent
            agent = violation.get("agent_name", "unknown")
            summary["by_agent"][agent] = summary["by_agent"].get(agent, 0) + 1

            # Count active violations
            if violation.get("status") == "active":
                summary["active_violations"] += 1

        return summary

    def _append_to_log(self, log_file: Path, entry: Dict[str, Any]) -> None:
        """Thread-safe log file appending."""
        with self._lock:
            try:
                existing_logs = self._read_log(log_file)
                existing_logs.append(entry)

                # Keep only last 10000 entries to prevent log files from growing too large
                if len(existing_logs) > 10000:
                    existing_logs = existing_logs[-10000:]

                log_file.write_text(json.dumps(existing_logs, indent=2))
            except Exception as e:
                logger.error(f"Failed to write to log file {log_file}: {e}")

    def _read_log(self, log_file: Path) -> List[Dict[str, Any]]:
        """Read and parse log file."""
        try:
            if log_file.exists():
                # Try multiple encodings for robust file reading
                encodings_to_try = ['utf-8', 'utf-8-sig', 'cp1252', 'iso-8859-1', 'latin1']

                for encoding in encodings_to_try:
                    try:
                        content = log_file.read_text(encoding=encoding)
                        return json.loads(content) if content.strip() else []
                    except UnicodeDecodeError:
                        continue

                # If all fail, use utf-8 with error replacement
                content = log_file.read_text(encoding='utf-8', errors='replace')
                return json.loads(content) if content.strip() else []
            return []
        except Exception as e:
            logger.error(f"Failed to read log file {log_file}: {e}")
            return []

    def _determine_error_severity(self, error_type: str) -> str:
        """Determine error severity based on error type."""
        critical_errors = ["security_violation", "data_corruption", "system_failure"]
        high_errors = ["validation_failure", "api_error", "processing_error"]

        if error_type in critical_errors:
            return "critical"
        elif error_type in high_errors:
            return "high"
        else:
            return "medium"

# Global logger instance
_logger_instance = None

def get_agent_logger() -> AgentLogger:
    """Get or create the global agent logger instance."""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = AgentLogger()
    return _logger_instance

# Backward compatibility function
def log_violation(entry: dict) -> str:
    """
    Backward compatibility function for existing code.

    Args:
        entry: Violation details

    Returns:
        Unique violation ID
    """
    logger_instance = get_agent_logger()
    return logger_instance.log_violation(entry)
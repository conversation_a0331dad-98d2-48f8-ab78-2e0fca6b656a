#!/usr/bin/env python3
"""
Test script to verify encoding fixes work correctly.
"""

import sys
import tempfile
import os
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, '.')

def test_load_sql_file():
    """Test the load_sql_file function with various encodings."""
    from utils.file_handler import load_sql_file
    
    # Test content with special characters that might cause encoding issues
    test_content = """
    -- SQL Test File with special characters
    -- This file contains various characters that might cause encoding issues
    
    SELECT 
        'Test with special chars: àáâãäåæçèéêë' as test_column,
        'More special chars: ñòóôõöøùúûüý' as test_column2,
        'Currency symbols: €£¥¢' as currency_test,
        'Math symbols: ±×÷≠≤≥' as math_test
    FROM test_table
    WHERE condition = 'test'
    """
    
    # Create temporary files with different encodings
    encodings_to_test = ['utf-8', 'utf-8-sig', 'cp1252', 'iso-8859-1']
    
    for encoding in encodings_to_test:
        print(f"Testing encoding: {encoding}")
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', encoding=encoding, suffix='.sql', delete=False) as f:
            f.write(test_content)
            temp_path = f.name
        
        try:
            # Test loading the file
            loaded_content = load_sql_file(temp_path)
            print(f"  ✅ Successfully loaded file with {encoding} encoding")
            print(f"  Content length: {len(loaded_content)} characters")
            
            # Verify content is reasonable
            if "SELECT" in loaded_content and "test_table" in loaded_content:
                print(f"  ✅ Content appears valid")
            else:
                print(f"  ⚠️  Content may be corrupted")
                
        except Exception as e:
            print(f"  ❌ Failed to load file with {encoding}: {e}")
        finally:
            # Clean up
            try:
                os.unlink(temp_path)
            except:
                pass
        
        print()

def test_problematic_file():
    """Test with a file that contains the problematic byte 0x8f."""
    from utils.file_handler import load_sql_file
    
    print("Testing with problematic byte 0x8f...")
    
    # Create a file with the problematic byte
    problematic_content = b"SELECT * FROM test WHERE column = '\x8f'"
    
    with tempfile.NamedTemporaryFile(mode='wb', suffix='.sql', delete=False) as f:
        f.write(problematic_content)
        temp_path = f.name
    
    try:
        loaded_content = load_sql_file(temp_path)
        print(f"✅ Successfully loaded problematic file")
        print(f"Content: {repr(loaded_content)}")
    except Exception as e:
        print(f"❌ Failed to load problematic file: {e}")
    finally:
        try:
            os.unlink(temp_path)
        except:
            pass

def main():
    """Run all tests."""
    print("🔍 Testing encoding fixes...")
    print("=" * 50)
    
    test_load_sql_file()
    test_problematic_file()
    
    print("=" * 50)
    print("✅ Encoding tests completed!")

if __name__ == "__main__":
    main()

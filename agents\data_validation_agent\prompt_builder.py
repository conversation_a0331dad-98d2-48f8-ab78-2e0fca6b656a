from pathlib import Path
import json

def build_data_validation_prompt(row_data: dict, rules_json: dict, reference_data: dict, historical_context: dict) -> str:
    template_path = Path("config/prompt_templates/data_validator_prompt.txt")
    if not template_path.exists():
        raise FileNotFoundError("Prompt template not found at:", template_path)

    # Try multiple encodings for robust file reading
    encodings_to_try = ['utf-8', 'utf-8-sig', 'cp1252', 'iso-8859-1', 'latin1']

    for encoding in encodings_to_try:
        try:
            template = template_path.read_text(encoding=encoding)
            break
        except UnicodeDecodeError:
            continue
    else:
        # If all fail, use utf-8 with error replacement
        template = template_path.read_text(encoding='utf-8', errors='replace')

    prompt = template.replace("{rules_json}", json.dumps(rules_json, indent=2))
    prompt = prompt.replace("{row_data}", json.dumps(row_data, indent=2))
    prompt = prompt.replace("{reference_data}", json.dumps(reference_data, indent=2))
    prompt = prompt.replace("{historical_context}", json.dumps(historical_context, indent=2))
    return prompt
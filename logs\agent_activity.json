[{"activity_id": "6c06b2d6-fd3a-433a-a873-0cf3e66de9e2", "timestamp": "2025-07-12T12:35:58.942284", "agent_name": "sp_parser_agent", "activity_type": "parse_stored_procedure", "status": "error", "details": {"procedure_name": "tmp7fg57m5b", "sql_length": 4257, "processing_time_ms": 15143, "input_size": 4257, "sqlglot_success": false, "sqlglot_error": "Invalid expression / Unexpected token. Line 75, Col: 10.\n  \n\n    COL_COUNT_TEMPLATE INT;\n\n    COL_COUNT_UPLOAD INT;\n\nBEGIN\n\n    -- Count expected columns\n\n    \u001b[4mSELECT\u001b[0m COUNT(*) INTO COL_COUNT_TEMPLATE \n\n    FROM SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS \n\n    WH", "output_size": 1979}, "processing_time_ms": 15143, "input_size": 4257, "output_size": 1979}, {"activity_id": "33482773-ad85-4ffa-881b-5d0d5c767bc9", "timestamp": "2025-07-12T12:36:27.672481", "agent_name": "sp_parser_agent", "activity_type": "parse_stored_procedure", "status": "error", "details": {"procedure_name": "tmpu9m94bm6", "sql_length": 4257, "processing_time_ms": 13515, "input_size": 4257, "sqlglot_success": false, "sqlglot_error": "Invalid expression / Unexpected token. Line 75, Col: 10.\n  \n\n    COL_COUNT_TEMPLATE INT;\n\n    COL_COUNT_UPLOAD INT;\n\nBEGIN\n\n    -- Count expected columns\n\n    \u001b[4mSELECT\u001b[0m COUNT(*) INTO COL_COUNT_TEMPLATE \n\n    FROM SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS \n\n    WH", "output_size": 1969}, "processing_time_ms": 13515, "input_size": 4257, "output_size": 1969}, {"activity_id": "44fe0bda-1e8f-4b18-be2a-2e57189cbdb1", "timestamp": "2025-07-12T12:37:21.731851", "agent_name": "sp_parser_agent", "activity_type": "parse_stored_procedure", "status": "error", "details": {"procedure_name": "tmpygtbbm9d", "sql_length": 4257, "processing_time_ms": 22347, "input_size": 4257, "sqlglot_success": false, "sqlglot_error": "Invalid expression / Unexpected token. Line 75, Col: 10.\n  \n\n    COL_COUNT_TEMPLATE INT;\n\n    COL_COUNT_UPLOAD INT;\n\nBEGIN\n\n    -- Count expected columns\n\n    \u001b[4mSELECT\u001b[0m COUNT(*) INTO COL_COUNT_TEMPLATE \n\n    FROM SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS \n\n    WH", "output_size": 2431}, "processing_time_ms": 22347, "input_size": 4257, "output_size": 2431}, {"activity_id": "3cf126dc-2c5e-4845-a63a-33a79f41b361", "timestamp": "2025-07-12T12:49:36.400116", "agent_name": "sp_parser_agent", "activity_type": "parse_stored_procedure", "status": "error", "details": {"procedure_name": "tmp0j1bwm0w", "sql_length": 4257, "processing_time_ms": 10627, "input_size": 4257, "sqlglot_success": false, "sqlglot_error": "Invalid expression / Unexpected token. Line 75, Col: 10.\n  \n\n    COL_COUNT_TEMPLATE INT;\n\n    COL_COUNT_UPLOAD INT;\n\nBEGIN\n\n    -- Count expected columns\n\n    \u001b[4mSELECT\u001b[0m COUNT(*) INTO COL_COUNT_TEMPLATE \n\n    FROM SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS \n\n    WH", "output_size": 1751}, "processing_time_ms": 10627, "input_size": 4257, "output_size": 1751}]
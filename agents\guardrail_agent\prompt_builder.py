from pathlib import Path
import json

def build_guardrail_prompt(agent_output: dict, column_template: dict, rules_json: dict) -> str:
    template_path = Path("config/prompt_templates/guardrail_prompt.txt")
    if not template_path.exists():
        raise FileNotFoundError("Prompt template not found at:", template_path)

    # Try multiple encodings for robust file reading
    encodings_to_try = ['utf-8', 'utf-8-sig', 'cp1252', 'iso-8859-1', 'latin1']

    for encoding in encodings_to_try:
        try:
            template = template_path.read_text(encoding=encoding)
            break
        except UnicodeDecodeError:
            continue
    else:
        # If all fail, use utf-8 with error replacement
        template = template_path.read_text(encoding='utf-8', errors='replace')

    prompt = template.replace("{agent_output_json}", json.dumps(agent_output, indent=2))
    prompt = prompt.replace("{column_template_json}", json.dumps(column_template, indent=2))
    prompt = prompt.replace("{rules_json}", json.dumps(rules_json, indent=2))
    return prompt
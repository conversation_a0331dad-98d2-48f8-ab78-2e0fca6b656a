[{"error_id": "c48a7e66-519a-4c7a-874b-f56d0a95869f", "timestamp": "2025-07-12T12:35:58.829879", "agent_name": "sp_parser_agent", "error_type": "schema_validation_warning", "error_message": "Response validation failed: [{'message': \"'procedure_name' is a required property\", 'path': [], 'invalid_value': {'procedures': [{'procedure_name': 'SP_VALIDATE_FILE_STRUCTURE', 'rules': [{'rule_type': 'column_count_check', 'logic_snippet': \"IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN RETURN 'File structure mismatch.'\", 'message': 'File structure must match the template structure'}]}, {'procedure_name': 'SP_VALIDATE_MANDATORY_COLUMNS', 'rules': [{'rule_type': 'mandatory_columns_check', 'logic_snippet': \"IF MISSING_COUNT > 0 THEN RETURN 'Missing required columns.'\", 'message': 'All required columns must be present'}]}, {'procedure_name': 'SP_CLEAN_DATA', 'rules': [{'columns': ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL'], 'rule_type': 'not_null_and_not_zero', 'message': 'Financial fields must not be null or zero'}, {'columns': ['UNIQUE_ID'], 'rule_type': 'not_start_with', 'value': 'TOTAL', 'message': \"Unique ID must not start with 'TOTAL'\"}, {'columns': ['UNIQUE_ID'], 'rule_type': 'remove_special_chars', 'message': 'Unique ID must not contain special characters'}]}, {'procedure_name': 'SP_FINAL_VALIDATION', 'rules': [{'rule_type': 'not_empty', 'logic_snippet': \"IF REC_COUNT = 0 THEN RETURN 'Validation failed: File is empty.'\", 'message': 'File must not be empty'}, {'columns': ['UNIQUE_ID'], 'rule_type': 'not_null_and_not_empty', 'message': 'Unique Identifier must not be null or empty'}, {'columns': ['UNIQUE_ID'], 'rule_type': 'no_duplicates', 'message': 'Unique Identifier must not have duplicates'}]}]}}]", "context": {"procedure_name": "tmp7fg57m5b", "validation_errors": [{"message": "'procedure_name' is a required property", "path": [], "invalid_value": {"procedures": [{"procedure_name": "SP_VALIDATE_FILE_STRUCTURE", "rules": [{"rule_type": "column_count_check", "logic_snippet": "IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN RETURN 'File structure mismatch.'", "message": "File structure must match the template structure"}]}, {"procedure_name": "SP_VALIDATE_MANDATORY_COLUMNS", "rules": [{"rule_type": "mandatory_columns_check", "logic_snippet": "IF MISSING_COUNT > 0 THEN RETURN 'Missing required columns.'", "message": "All required columns must be present"}]}, {"procedure_name": "SP_CLEAN_DATA", "rules": [{"columns": ["NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL"], "rule_type": "not_null_and_not_zero", "message": "Financial fields must not be null or zero"}, {"columns": ["UNIQUE_ID"], "rule_type": "not_start_with", "value": "TOTAL", "message": "Unique ID must not start with 'TOTAL'"}, {"columns": ["UNIQUE_ID"], "rule_type": "remove_special_chars", "message": "Unique ID must not contain special characters"}]}, {"procedure_name": "SP_FINAL_VALIDATION", "rules": [{"rule_type": "not_empty", "logic_snippet": "IF REC_COUNT = 0 THEN RETURN 'Validation failed: File is empty.'", "message": "File must not be empty"}, {"columns": ["UNIQUE_ID"], "rule_type": "not_null_and_not_empty", "message": "Unique Identifier must not be null or empty"}, {"columns": ["UNIQUE_ID"], "rule_type": "no_duplicates", "message": "Unique Identifier must not have duplicates"}]}]}}]}, "stack_trace": null, "severity": "medium", "resolved": false}, {"error_id": "ac13a820-59c5-4b22-a9e7-2d45c1fff88e", "timestamp": "2025-07-12T12:35:58.924744", "agent_name": "sp_parser_agent", "error_type": "response_validation_error", "error_message": "Failed to validate or parse response: 2 validation errors for RuleExtractionOutput\nprocedure_name\n  Field required [type=missing, input_value={'procedures': [{'procedu...ot have duplicates'}]}]}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\nrules\n  Field required [type=missing, input_value={'procedures': [{'procedu...ot have duplicates'}]}]}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", "context": {"procedure_name": "tmp7fg57m5b", "response": "{\n  \"procedures\": [\n    {\n      \"procedure_name\": \"SP_VALIDATE_FILE_STRUCTURE\",\n      \"rules\": [\n        {\n          \"rule_type\": \"column_count_check\",\n          \"logic_snippet\": \"IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN RETURN 'File structure mismatch.'\",\n          \"message\": \"File structure must match the template structure\"\n        }\n      ]\n    },\n    {\n      \"procedure_name\": \"SP_VALIDATE_MANDATORY_COLUMNS\",\n      \"rules\": [\n        {\n          \"rule_type\": \"mandatory_columns_check\",\n          \"logic_snippet\": \"IF MISSING_COUNT > 0 THEN RETURN 'Missing required columns.'\",\n          \"message\": \"All required columns must be present\"\n        }\n      ]\n    },\n    {\n      \"procedure_name\": \"SP_CLEAN_DATA\",\n      \"rules\": [\n        {\n          \"columns\": [\"NAV\", \"OWNERSHIP_PERCENTAGE\", \"CAPITAL_CALLED\", \"NO_OF_SHARES\", \"COMMITTED_CAPITAL\"],\n          \"rule_type\": \"not_null_and_not_zero\",\n          \"message\": \"Financial fields must not be null or zero\"\n        },\n        {\n       "}, "stack_trace": "Traceback (most recent call last):\n  File \"D:\\64-SQUARES\\CBRE_Validation_Agent\\agents\\sp_parser_agent\\agent.py\", line 111, in parse_stored_procedure\n    output = RuleExtractionOutput.model_validate_json(response)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pydantic\\main.py\", line 656, in model_validate_json\n    return cls.__pydantic_validator__.validate_json(json_data, strict=strict, context=context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 2 validation errors for RuleExtractionOutput\nprocedure_name\n  Field required [type=missing, input_value={'procedures': [{'procedu...ot have duplicates'}]}]}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\nrules\n  Field required [type=missing, input_value={'procedures': [{'procedu...ot have duplicates'}]}]}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\n", "severity": "medium", "resolved": false}, {"error_id": "4fe28031-4b16-45a8-b648-9379f22de4dc", "timestamp": "2025-07-12T12:36:27.591736", "agent_name": "sp_parser_agent", "error_type": "schema_validation_warning", "error_message": "Response validation failed: [{'message': \"'procedure_name' is a required property\", 'path': [], 'invalid_value': {'procedures': [{'procedure_name': 'SP_VALIDATE_FILE_STRUCTURE', 'rules': [{'rule_type': 'column_count_check', 'logic_snippet': \"IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN RETURN 'File structure mismatch.'\", 'message': 'File structure must match the template'}]}, {'procedure_name': 'SP_VALIDATE_MANDATORY_COLUMNS', 'rules': [{'rule_type': 'mandatory_columns_check', 'logic_snippet': \"IF MISSING_COUNT > 0 THEN RETURN 'Missing required columns.'\", 'message': 'All required columns must be present'}]}, {'procedure_name': 'SP_CLEAN_DATA', 'rules': [{'columns': ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL'], 'rule_type': 'not_null_and_not_zero', 'message': 'Financial fields must not be null or zero'}, {'columns': ['UNIQUE_ID'], 'rule_type': 'not_start_with', 'value': 'TOTAL', 'message': \"Unique ID must not start with 'TOTAL'\"}, {'columns': ['UNIQUE_ID'], 'rule_type': 'remove_special_chars', 'message': 'Unique ID must not contain special characters'}]}, {'procedure_name': 'SP_FINAL_VALIDATION', 'rules': [{'rule_type': 'not_empty', 'logic_snippet': \"IF REC_COUNT = 0 THEN RETURN 'Validation failed: File is empty.'\", 'message': 'File must not be empty'}, {'columns': ['UNIQUE_ID'], 'rule_type': 'not_null_and_not_empty', 'message': 'Unique Identifier must not be null or empty'}, {'columns': ['UNIQUE_ID'], 'rule_type': 'no_duplicates', 'message': 'Unique Identifier must not have duplicates'}]}]}}]", "context": {"procedure_name": "tmpu9m94bm6", "validation_errors": [{"message": "'procedure_name' is a required property", "path": [], "invalid_value": {"procedures": [{"procedure_name": "SP_VALIDATE_FILE_STRUCTURE", "rules": [{"rule_type": "column_count_check", "logic_snippet": "IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN RETURN 'File structure mismatch.'", "message": "File structure must match the template"}]}, {"procedure_name": "SP_VALIDATE_MANDATORY_COLUMNS", "rules": [{"rule_type": "mandatory_columns_check", "logic_snippet": "IF MISSING_COUNT > 0 THEN RETURN 'Missing required columns.'", "message": "All required columns must be present"}]}, {"procedure_name": "SP_CLEAN_DATA", "rules": [{"columns": ["NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL"], "rule_type": "not_null_and_not_zero", "message": "Financial fields must not be null or zero"}, {"columns": ["UNIQUE_ID"], "rule_type": "not_start_with", "value": "TOTAL", "message": "Unique ID must not start with 'TOTAL'"}, {"columns": ["UNIQUE_ID"], "rule_type": "remove_special_chars", "message": "Unique ID must not contain special characters"}]}, {"procedure_name": "SP_FINAL_VALIDATION", "rules": [{"rule_type": "not_empty", "logic_snippet": "IF REC_COUNT = 0 THEN RETURN 'Validation failed: File is empty.'", "message": "File must not be empty"}, {"columns": ["UNIQUE_ID"], "rule_type": "not_null_and_not_empty", "message": "Unique Identifier must not be null or empty"}, {"columns": ["UNIQUE_ID"], "rule_type": "no_duplicates", "message": "Unique Identifier must not have duplicates"}]}]}}]}, "stack_trace": null, "severity": "medium", "resolved": false}, {"error_id": "4f79e144-2a68-464a-99b4-974c4a1ec113", "timestamp": "2025-07-12T12:36:27.623913", "agent_name": "sp_parser_agent", "error_type": "response_validation_error", "error_message": "Failed to validate or parse response: 2 validation errors for RuleExtractionOutput\nprocedure_name\n  Field required [type=missing, input_value={'procedures': [{'procedu...ot have duplicates'}]}]}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\nrules\n  Field required [type=missing, input_value={'procedures': [{'procedu...ot have duplicates'}]}]}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing", "context": {"procedure_name": "tmpu9m94bm6", "response": "{\n  \"procedures\": [\n    {\n      \"procedure_name\": \"SP_VALIDATE_FILE_STRUCTURE\",\n      \"rules\": [\n        {\n          \"rule_type\": \"column_count_check\",\n          \"logic_snippet\": \"IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN RETURN 'File structure mismatch.'\",\n          \"message\": \"File structure must match the template\"\n        }\n      ]\n    },\n    {\n      \"procedure_name\": \"SP_VALIDATE_MANDATORY_COLUMNS\",\n      \"rules\": [\n        {\n          \"rule_type\": \"mandatory_columns_check\",\n          \"logic_snippet\": \"IF MISSING_COUNT > 0 THEN RETURN 'Missing required columns.'\",\n          \"message\": \"All required columns must be present\"\n        }\n      ]\n    },\n    {\n      \"procedure_name\": \"SP_CLEAN_DATA\",\n      \"rules\": [\n        {\n          \"columns\": [\"NAV\", \"OWNERSHIP_PERCENTAGE\", \"CAPITAL_CALLED\", \"NO_OF_SHARES\", \"COMMITTED_CAPITAL\"],\n          \"rule_type\": \"not_null_and_not_zero\",\n          \"message\": \"Financial fields must not be null or zero\"\n        },\n        {\n          \"column"}, "stack_trace": "Traceback (most recent call last):\n  File \"D:\\64-SQUARES\\CBRE_Validation_Agent\\agents\\sp_parser_agent\\agent.py\", line 111, in parse_stored_procedure\n    output = RuleExtractionOutput.model_validate_json(response)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pydantic\\main.py\", line 656, in model_validate_json\n    return cls.__pydantic_validator__.validate_json(json_data, strict=strict, context=context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npydantic_core._pydantic_core.ValidationError: 2 validation errors for RuleExtractionOutput\nprocedure_name\n  Field required [type=missing, input_value={'procedures': [{'procedu...ot have duplicates'}]}]}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\nrules\n  Field required [type=missing, input_value={'procedures': [{'procedu...ot have duplicates'}]}]}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.10/v/missing\n", "severity": "medium", "resolved": false}, {"error_id": "0ced3df3-4499-4a37-8e0e-b7c3f41d9397", "timestamp": "2025-07-12T12:37:21.621137", "agent_name": "sp_parser_agent", "error_type": "json_parsing_error", "error_message": "Failed to parse JSON response: [{'type': 'json_parse_error', 'message': 'Invalid JSON: Extra data: line 10 column 2 (char 342)', 'position': 342}]", "context": {"procedure_name": "tmpygtbbm9d", "response_preview": "{\n  \"procedure_name\": \"SP_VALIDATE_FILE_STRUCTURE\",\n  \"rules\": [\n    {\n      \"rule_type\": \"column_count_check\",\n      \"logic_snippet\": \"IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN RETURN 'File structure mismatch.'\",\n      \"message\": \"File structure mismatch. Expected columns count from template and uploaded file should match\"\n    }\n  ]\n},\n{\n  \"procedure_name\": \"SP_VALIDATE_MANDATORY_COLUMNS\",\n  \"rules\": [\n    {\n      \"rule_type\": \"mandatory_columns_check\",\n      \"logic_snippet\": \"IF MISSING_C"}, "stack_trace": null, "severity": "medium", "resolved": false}, {"error_id": "682c4140-5ebd-459c-aa02-a782238126b1", "timestamp": "2025-07-12T12:37:21.636724", "agent_name": "sp_parser_agent", "error_type": "response_validation_error", "error_message": "Failed to validate or parse response: Failed to parse JSON response: [{'type': 'json_parse_error', 'message': 'Invalid JSON: Extra data: line 10 column 2 (char 342)', 'position': 342}]", "context": {"procedure_name": "tmpygtbbm9d", "response": "{\n  \"procedure_name\": \"SP_VALIDATE_FILE_STRUCTURE\",\n  \"rules\": [\n    {\n      \"rule_type\": \"column_count_check\",\n      \"logic_snippet\": \"IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN RETURN 'File structure mismatch.'\",\n      \"message\": \"File structure mismatch. Expected columns count from template and uploaded file should match\"\n    }\n  ]\n},\n{\n  \"procedure_name\": \"SP_VALIDATE_MANDATORY_COLUMNS\",\n  \"rules\": [\n    {\n      \"rule_type\": \"mandatory_columns_check\",\n      \"logic_snippet\": \"IF MISSING_COUNT > 0 THEN RETURN 'Missing required columns.'\",\n      \"message\": \"All required columns must be present in the uploaded file\"\n    }\n  ]\n},\n{\n  \"procedure_name\": \"SP_CLEAN_DATA\",\n  \"rules\": [\n    {\n      \"columns\": [\"NAV\", \"OWNERSHIP_PERCENTAGE\", \"CAPITAL_CALLED\", \"NO_OF_SHARES\", \"COMMITTED_CAPITAL\"],\n      \"rule_type\": \"not_null_and_not_zero\",\n      \"logic_snippet\": \"DELETE FROM FILE_UPLOAD_STAGE WHERE (NVL(NAV,0) = 0) AND (NVL(OWNERSHIP_PERCENTAGE,0) = 0) AND (NVL(CAPITAL_CALLED,0) = 0) AND ("}, "stack_trace": "Traceback (most recent call last):\n  File \"D:\\64-SQUARES\\CBRE_Validation_Agent\\agents\\sp_parser_agent\\agent.py\", line 101, in parse_stored_procedure\n    raise ValueError(error_msg)\nValueError: Failed to parse JSON response: [{'type': 'json_parse_error', 'message': 'Invalid JSON: Extra data: line 10 column 2 (char 342)', 'position': 342}]\n", "severity": "medium", "resolved": false}]